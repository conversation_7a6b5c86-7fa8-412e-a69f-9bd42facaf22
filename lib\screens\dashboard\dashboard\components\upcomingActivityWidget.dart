import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/text/dsSansText.dart';
import 'package:seawork/components/text/openSansText.dart';
import 'package:seawork/router.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/screens/employee/employee/providers/meetingProvider.dart';
import 'package:seawork/screens/employee/employee/models/meeting.dart';
import 'package:seawork/utils/util.dart';
import 'package:go_router/go_router.dart';

class UpcomingActivityWidget extends ConsumerStatefulWidget {
  const UpcomingActivityWidget({super.key});

  @override
  _UpcomingActivityWidgetState createState() => _UpcomingActivityWidgetState();
}

class _UpcomingActivityWidgetState
    extends ConsumerState<UpcomingActivityWidget> {
  int currentPage = 0;
  bool _isNavigating = false; // Prevent double taps

  @override
  void initState() {
    super.initState();
    // Fetch events when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final now = DateTime.now();
      final endDate = now.add(
        const Duration(days: 365),
      ); // Get meetings for the next year, or adjust as needed

      ref
          .read(graphCalendarProvider.notifier)
          .fetchEventsForDateRange(now, endDate);
    });
  }

  @override
  Widget build(BuildContext context) {
    final calendarState = ref.watch(graphCalendarProvider);
    final upcomingMeetings = _getUpcomingMeetings(calendarState.meetings);

    if (calendarState.isLoading ||
        calendarState.error != null ||
        upcomingMeetings.isEmpty) {
      return Container();
    }

    return Center(
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.whiteColor,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: AppColors.boxshadowcolor.withOpacity(0.25),
              blurRadius: 9.6,
              offset: Offset(0, 0),
            ),
          ],
          border: Border.all(color: AppColors.lightgraygreen, width: 0.5),
        ),
        child: Padding(
          padding: const EdgeInsets.only(
            left: 20,
            right: 20,
            top: 16,
            bottom: 12,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  DMSans600Large(
                    14,
                    'Upcoming activity',
                    AppColors.dashboardheading,
                  ),
                  GestureDetector(
                    onTap: () {
                      // Update bottom nav bar state and navigate
                      ref.read(bottomNavBarProvider.notifier).state = 1;
                      context.push('/calendar');
                    },
                    child: OpenSans400Large(
                      12,
                      'View all',
                      AppColors.lightBlack,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 14),

              // Show meeting content
              _buildMeetingsPageView(upcomingMeetings),

              SizedBox(height: 12),
              // Pagination dots
              if (upcomingMeetings.isNotEmpty)
                _buildPaginationDots(upcomingMeetings.length),
            ],
          ),
        ),
      ),
    );
  }

  List<Meeting> _getUpcomingMeetings(List<Meeting> meetings) {
    final now = DateTime.now();
    final futureMeetings =
        meetings.where((meeting) {
          if (meeting.isCancelled) return false;

          try {
            final meetingDateTime = _parseMeetingDateTime(meeting);
            final today = DateTime(now.year, now.month, now.day);
            return meetingDateTime.isAfter(
              today.subtract(Duration(seconds: 1)),
            );
          } catch (e) {
            print('Error parsing meeting time for ${meeting.title}: $e');
            return false;
          }
        }).toList();

    futureMeetings.sort((a, b) {
      final dateTimeA = _parseMeetingDateTime(a);
      final dateTimeB = _parseMeetingDateTime(b);
      return dateTimeA.compareTo(dateTimeB);
    });
    return futureMeetings.take(3).toList();
  }

  Widget _buildPaginationDots(int totalMeetings) {
    return Center(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(totalMeetings, (index) {
          return GestureDetector(
            onTap: () {
              setState(() {
                currentPage = index;
              });
            },
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 3),
              width: 15,
              height: 2,
              decoration: BoxDecoration(
                color:
                    index == currentPage
                        ? AppColors.viewColor
                        : AppColors.viewColor.withOpacity(0.3),
                borderRadius: BorderRadius.circular(9),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildMeetingsPageView(List<Meeting> meetings) {
    return SizedBox(
      height: 100,
      child: PageView.builder(
        controller: PageController(
          initialPage: currentPage,
          viewportFraction: 1.0,
        ),
        onPageChanged: (int page) {
          setState(() {
            currentPage = page;
          });
        },
        itemCount: meetings.length,
        itemBuilder: (context, index) {
          final meeting = meetings[index];

          return Padding(
            padding: const EdgeInsets.only(right: 5.0),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(8),
                splashColor: AppColors.microinteraction,
                highlightColor: AppColors.microinteraction,
                onTap: () async {
                  if (_isNavigating) return;
                  _isNavigating = true;
                  try {
                    await Future.delayed(const Duration(milliseconds: 100));

                    if (!mounted) {
                      _isNavigating = false;
                      return;
                    }
                    appRouter.push('/meeting-details', extra: meeting);
                    _isNavigating = false;
                  } catch (e) {
                    print('Navigation error: $e');
                    _isNavigating = false;
                  }
                },
                child: _buildMeetingContent(meeting),
              ),
            ),
          );
        },
      ),
    );
  }

  String _getMeetingDescription(Meeting meeting) {
    if (meeting.description != null && meeting.description!.trim().isNotEmpty) {
      String description = meeting.description!.trim();
      if (description.length > 60) {
        description = description.substring(0, 60) + '...';
      }
      return description;
    } else {
      return "";
    }
  }

  DateTime _parseMeetingDateTime(Meeting meeting) {
    try {
      // Parse the date (format: yyyy-MM-dd)
      final dateParts = meeting.date.split('-');
      final year = int.parse(dateParts[0]);
      final month = int.parse(dateParts[1]);
      final day = int.parse(dateParts[2]);

      // Parse the time (format: HH:mm)
      final timeParts = meeting.time.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      // Create DateTime in UTC then convert to local
      final utcDateTime = DateTime.utc(year, month, day, hour, minute);
      return utcDateTime.toLocal();
    } catch (e) {
      print('Error parsing meeting date/time: $e');
      rethrow;
    }
  }

  String _formatMeetingDate(Meeting meeting) {
    try {
      final meetingDateTime = _parseMeetingDateTime(meeting);
      final now = DateTime.now();

      if (meetingDateTime.year == now.year &&
          meetingDateTime.month == now.month &&
          meetingDateTime.day == now.day) {
        return 'Today';
      }

      final tomorrow = now.add(Duration(days: 1));
      if (meetingDateTime.year == tomorrow.year &&
          meetingDateTime.month == tomorrow.month &&
          meetingDateTime.day == tomorrow.day) {
        return 'Tomorrow';
      }

      final daysDifference = meetingDateTime.difference(now).inDays;
      if (daysDifference >= 0 && daysDifference <= 7) {
        return DateFormat('EEEE').format(meetingDateTime);
      } else {
        return DateFormat('MMM d').format(meetingDateTime);
      }
    } catch (e) {
      return 'Today'; // Fallback
    }
  }

  String _formatMeetingTime(Meeting meeting) {
    try {
      final meetingDateTime = _parseMeetingDateTime(meeting);
      return DateFormat.jm().format(meetingDateTime);
    } catch (e) {
      return meeting.time;
    }
  }

  String _getMeetingLocation(Meeting meeting) {
    // If there's a physical location, show it
    if (meeting.location != null && meeting.location!.isNotEmpty) {
      return capitalizeFirstWordOnly(meeting.location!);
    } else {
      // No location given, check if it's an online meeting
      if (meeting.isOnline && meeting.onlineMeetingProvider.isNotEmpty) {
        if (meeting.onlineMeetingProvider == 'teamsForBusiness') {
          return "Microsoft Teams";
        } else {
          return capitalizeFirstWordOnly(meeting.onlineMeetingProvider);
        }
      } else {
        // Default to Online if no location is specified
        return "Online";
      }
    }
  }

  Widget _buildMeetingContent(Meeting meeting) {
    return Container(
      height: 92,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.viewColor, width: 0.5),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CustomSvgImage(
                        imageName: "classmeeting",
                        height: 24,
                        width: 24,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Column(
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 3,
                                  child: DmSansText(
                                    meeting.title,
                                    color: AppColors.textColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                SizedBox(width: 8),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    CustomSvgImage(
                                      imageName: "greendot",
                                      height: 6,
                                      width: 6,
                                    ),
                                    SizedBox(width: 4),
                                    ConstrainedBox(
                                      constraints: BoxConstraints(maxWidth: 80),
                                      child: OpenSansText(
                                        _getMeetingLocation(meeting),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w400,
                                        color: AppColors.darkGreen,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  if (_getMeetingDescription(meeting).isNotEmpty) ...[
                    SizedBox(height: 12),
                    Text.rich(
                  TextSpan(
                    text: _getMeetingDescription(meeting),
                    style: GoogleFonts.openSans(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: AppColors.darkGreyColor,
                      height: 1.4,
                    ),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                )
                  ],
                  SizedBox(height: 5),
                  Row(
                    children: [
                      OpenSansText(
                        _formatMeetingDate(meeting),
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppColors.darkGreyColor,
                      ),
                      SizedBox(width: 4),
                      Center(
                        child: CustomSvgImage(
                          imageName: "tealdot",
                          height: 4,
                          width: 4,
                        ),
                      ),
                      SizedBox(width: 4),
                      OpenSansText(
                        _formatMeetingTime(meeting),
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppColors.darkGreyColor,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
