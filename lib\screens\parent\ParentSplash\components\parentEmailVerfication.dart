import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/screens/public/login/otp.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/screens/public/registration/provider/registrationProvider.dart';
import 'package:seawork/screens/public/login/login.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:go_router/go_router.dart';

class ParentEmailVerificationScreen extends ConsumerStatefulWidget {
  final bool forRegistration;
  const ParentEmailVerificationScreen({
    super.key,
    this.forRegistration = false,
  });

  @override
  ConsumerState<ParentEmailVerificationScreen> createState() =>
      _ParentEmailVerificationScreenState();
}

class _ParentEmailVerificationScreenState
    extends ConsumerState<ParentEmailVerificationScreen> {
  final TextEditingController emailController = TextEditingController();
  bool _isRequestingOtp = false;

  @override
  void dispose() {
    emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final otpState = ref.watch(sendSignUpEmailOtpProvider);
    _isRequestingOtp = otpState.isLoading;
    return Scaffold(
      body: Stack(
        children: [
          // Background Image
          Positioned.fill(
            child: SvgPicture.asset(
              'assets/images/loginbackground.svg',
              fit: BoxFit.cover,
            ),
          ),

          // Dark overlay
          Positioned.fill(
            child: Container(color: Colors.black.withOpacity(0.4)),
          ),

          // Main content
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Logo
                  SvgPicture.asset(
                    'assets/images/logoz.svg',
                    height: 100,
                    width: 100,
                  ),
                  const SizedBox(height: 40),

                  // Verification container
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title
                        DmSansText(
                          "Let's confirm it's you!",
                          fontSize: 24,
                          fontWeight: FontWeight.w600,
                          color: AppColors.whiteColor,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),

                        // Subtitle
                        DmSansText(
                          'Please verify your\nemail-ID to continue',
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: AppColors.whiteColor,
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 30),
                        Column(
                          children: [
                            DmSansText(
                              'Enter your Email-ID',
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: AppColors.whiteColor,
                            ),
                          ],
                        ),
                        const SizedBox(height: 24), // Mobile input field
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: TextField(
                                  style: const TextStyle(
                                    color: AppColors.whiteColor,
                                  ),
                                  decoration: InputDecoration(
                                    hintText: '<EMAIL>',
                                    hintStyle: TextStyle(
                                      color: AppColors.whiteColor.withOpacity(
                                        0.5,
                                      ),
                                    ),
                                    border: InputBorder.none,
                                  ),
                                  keyboardType: TextInputType.emailAddress,
                                  controller: emailController,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 8),

                        // Email option
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            TextButton(
                              onPressed: () {
                                context.push(
                                  '/parent-mobile-verification',
                                  extra: {
                                    'forRegistration': widget.forRegistration,
                                  },
                                );
                              },
                              child: DmSansText(
                                'Use mobile number',
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppColors.whiteColor,
                                underline: true,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 30),

                        // Get OTP button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed:
                                _isRequestingOtp
                                    ? null
                                    : () async {
                                      final email = emailController.text.trim();
                                      if (email.isEmpty) {
                                        Fluttertoast.showToast(
                                          msg: 'Please enter an email',
                                        );
                                        return;
                                      }
                                      setState(() => _isRequestingOtp = true);
                                      try {
                                        if (widget.forRegistration) {
                                          await ref
                                              .read(
                                                sendSignUpEmailOtpProvider
                                                    .notifier,
                                              )
                                              .sendOtp(email);
                                          final state = ref.read(
                                            sendSignUpEmailOtpProvider,
                                          );
                                          state.when(
                                            data: (data) async {
                                              final error =
                                                  data['error']?.toString() ??
                                                  '';
                                              final otp =
                                                  data['otp']?.toString() ?? '';
                                              if (error ==
                                                  'User already exist with same email id ') {
                                                // Show BottomSheet
                                                showModalBottomSheet(
                                                  context: context,
                                                  shape: const RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.vertical(
                                                          top: Radius.circular(
                                                            24,
                                                          ),
                                                        ),
                                                  ),
                                                  isScrollControlled: true,
                                                  builder: (context) {
                                                    return Padding(
                                                      padding:
                                                          const EdgeInsets.only(
                                                            left: 16,
                                                            right: 16,
                                                            top: 24,
                                                            bottom: 24,
                                                          ),
                                                      child: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          const Text(
                                                            'An account already exists with this email',
                                                            style: TextStyle(
                                                              color: Colors.red,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              fontSize: 16,
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                            height: 24,
                                                          ),
                                                          SizedBox(
                                                            width:
                                                                double.infinity,
                                                            child: ElevatedButton(
                                                              style: ElevatedButton.styleFrom(
                                                                backgroundColor:
                                                                    AppColors
                                                                        .viewColor,
                                                                foregroundColor:
                                                                    AppColors
                                                                        .whiteColor,
                                                                padding:
                                                                    const EdgeInsets.symmetric(
                                                                      vertical:
                                                                          16,
                                                                    ),
                                                                shape: RoundedRectangleBorder(
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                        8,
                                                                      ),
                                                                ),
                                                              ),
                                                              onPressed: () {
                                                                Navigator.pop(
                                                                  context,
                                                                ); // Close bottom sheet
                                                                Navigator.pushReplacement(
                                                                  context,
                                                                  MaterialPageRoute(
                                                                    builder:
                                                                        (
                                                                          context,
                                                                        ) => Login(
                                                                          // You may want to pass the email to Login screen
                                                                        ),
                                                                  ),
                                                                );
                                                              },
                                                              child: DmSansText(
                                                                'Login to existing account',
                                                                fontSize: 16,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color:
                                                                    AppColors
                                                                        .whiteColor,
                                                              ),
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                            height: 12,
                                                          ),
                                                          SizedBox(
                                                            width:
                                                                double.infinity,
                                                            child: OutlinedButton(
                                                              style: OutlinedButton.styleFrom(
                                                                side: const BorderSide(
                                                                  color:
                                                                      AppColors
                                                                          .viewColor,
                                                                ),
                                                                padding:
                                                                    const EdgeInsets.symmetric(
                                                                      vertical:
                                                                          16,
                                                                    ),
                                                                shape: RoundedRectangleBorder(
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                        8,
                                                                      ),
                                                                ),
                                                              ),
                                                              onPressed: () {
                                                                Navigator.pop(
                                                                  context,
                                                                ); // Just close bottom sheet
                                                              },
                                                              child: DmSansText(
                                                                'Try a different email',
                                                                fontSize: 16,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color:
                                                                    AppColors
                                                                        .viewColor,
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    );
                                                  },
                                                );
                                              } else if (error.isEmpty &&
                                                  otp.isNotEmpty) {
                                                Fluttertoast.showToast(
                                                  msg: 'OTP: $otp',
                                                  toastLength:
                                                      Toast.LENGTH_LONG,
                                                  timeInSecForIosWeb: 5,
                                                );
                                                Future.delayed(
                                                  const Duration(seconds: 5),
                                                  () {
                                                    Fluttertoast.cancel();
                                                  },
                                                );
                                                Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder:
                                                        (context) => OtpScreen(
                                                          email: email,
                                                          otp: otp,
                                                          forRegistrationEmail:
                                                              true,
                                                        ),
                                                  ),
                                                );
                                              } else {
                                                Fluttertoast.showToast(
                                                  msg:
                                                      error.isNotEmpty
                                                          ? error
                                                          : 'Failed to get OTP',
                                                );
                                              }
                                            },
                                            loading:
                                                () => Fluttertoast.showToast(
                                                  msg: 'Sending OTP...',
                                                ),
                                            error:
                                                (e, _) =>
                                                    Fluttertoast.showToast(
                                                      msg: 'Error: $e',
                                                    ),
                                          );
                                        } else {
                                          // Handle login flow if needed
                                        }
                                      } finally {
                                        setState(
                                          () => _isRequestingOtp = false,
                                        );
                                      }
                                    },
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  _isRequestingOtp
                                      ? AppColors.lightGreyColor2
                                      : AppColors.whiteColor,
                              foregroundColor: AppColors.blackColor,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child:
                                _isRequestingOtp
                                    ? const SizedBox(
                                      width: 18,
                                      height: 18,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                      ),
                                    )
                                    : DmSansText(
                                      'Get OTP',
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.blackColor,
                                    ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
