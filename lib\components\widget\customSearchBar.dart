import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/utils/style/colors.dart';

class CustomSearchField extends StatelessWidget {
  final bool showSearchField;
  final TextEditingController searchController;
  final VoidCallback onSearch;
  final VoidCallback toggleSearch;
  final VoidCallback? onClear;
  final bool isLoading;
  final FocusNode? focusNode;
  const CustomSearchField({
    Key? key,
    required this.showSearchField,
    required this.searchController,
    required this.onSearch,
    required this.toggleSearch,
    this.onClear,
    this.isLoading = false,
    this.focusNode, // Add this parameter
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!showSearchField) return SizedBox.shrink();

    return AbsorbPointer(
      absorbing: isLoading,
      child: Opacity(
        opacity: isLoading ? 0.5 : 1.0,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Flexible(
              child: Padding(
                padding: const EdgeInsets.only(left: 4),
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.inputfillColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextField(
                    controller: searchController,
                    focusNode: focusNode,
                    enabled: !isLoading,
                    autofocus: true,
                    onChanged: (value) {
                      if (value.isEmpty) {
                        onSearch();
                      }
                    },
                    onSubmitted: (value) => onSearch(),
                    decoration: InputDecoration(
                      hintText: "Search by type, person name",
                      hintStyle: GoogleFonts.openSans(
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                        color: AppColors.lightGreyColor,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        vertical: 10,
                        horizontal: 16,
                      ),
                      suffixIcon: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          IconButton(
                            icon: CustomSvgImage(
                              imageName: "search-alt-1_svgrepo.com",
                              width: 24,
                              height: 24,
                            ),
                            onPressed: isLoading ? null : onSearch,
                          ),
                          IconButton(
                            icon: const Icon(
                              Icons.clear,
                              color: AppColors.lightGreyColor,
                            ),
                            onPressed:
                                isLoading
                                    ? null
                                    : () {
                                      searchController.clear();
                                      if (onClear != null) {
                                      onClear!();
                                      } else {
                                      onSearch();
                                      }
                                      toggleSearch();
                                    },
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
