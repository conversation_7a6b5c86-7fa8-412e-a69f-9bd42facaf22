import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:seawork/screens/student/student/studentAttendance.dart';
import 'package:go_router/go_router.dart';
// import 'package:seawork/screens/student/student/components/studentWithdraw.dart';

final navigationProvider = StateProvider<Widget>((ref) => DashboardContent());

class DashboardScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentScreen = ref.watch(navigationProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Welcome to SEA'),
        centerTitle: true,
        backgroundColor: Colors.deepPurple,
        actions: [
          IconButton(icon: const Icon(Icons.notifications), onPressed: () {}),
        ],
      ),
      drawer: Drawer(
        child: Column(
          children: [
            UserAccountsDrawerHeader(
              decoration: const BoxDecoration(color: Colors.teal),
              accountName: const Text(
                'Admin User',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              accountEmail: const Text('<EMAIL>'),
              currentAccountPicture: CircleAvatar(
                backgroundColor: Colors.white,
                child: const Icon(Icons.person, size: 40, color: Colors.black),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.dashboard, color: Colors.black),
              title: const Text('Dashboard'),
              onTap: () {},
            ),
            ListTile(
              leading: const Icon(Icons.school, color: Colors.black),
              title: const Text('Student Management'),
              onTap: () {
                context.push('/student-list');
              },
            ),
            // ListTile(
            //   leading: const Icon(Icons.school, color: Colors.black),
            //   title: const Text('Request Management'),
            //   onTap: () {
            //     Navigator.push(
            //         context,
            //         MaterialPageRoute(
            //           builder: (context) => StudentWidrawDataScreen(),
            //         ));
            //   },
            // ),
            ListTile(
              leading: const Icon(Icons.people, color: Colors.black),
              title: const Text('Employee Management'),
              onTap: () {
                context.push('/employee-management');
              },
            ),
            ListTile(
              leading: const Icon(Icons.people, color: Colors.black),
              title: const Text('Leave Management'),
              onTap: () {
                //  Navigator.push(context, MaterialPageRoute(builder: (context) =>
                //  AbsenceTypesScreen()
                //  ,));
              },
            ),
            ListTile(
              leading: const Icon(Icons.fact_check, color: Colors.black),
              title: const Text('Attendence'),
              onTap: () {
                // Navigator.push(
                //     context,
                //     MaterialPageRoute(
                //       builder: (context) => StudentAbsentList(),
                //     ));
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings, color: Colors.black),
              title: const Text('Settings'),
              onTap: () {},
            ),
          ],
        ),
      ),
    );
  }
}

class DashboardContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: const [
          Icon(Icons.dashboard, size: 100, color: Colors.deepPurple),
          SizedBox(height: 20),
          Text(
            'Welcome to SEA Dashboard',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
