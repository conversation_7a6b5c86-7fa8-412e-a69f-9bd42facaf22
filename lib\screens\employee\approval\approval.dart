import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:seawork/components/commonWidgets/customImage.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/customLoaderWithMessages.dart';
import 'package:seawork/components/widget/customSearchBar.dart';
import 'package:seawork/components/widget/noDataFound.dart';
import 'package:seawork/components/widget/customCompactCalendar.dart';
import 'package:seawork/utils/style/colors.dart';
import 'package:seawork/components/mixed/customBottomNavigationBar/customBottomNavigationBar.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customTabBarCard.dart';
import 'package:seawork/components/widget/customStatusTabbar.dart';
import 'package:seawork/components/widget/customLoader.dart';
import 'package:seawork/screens/employee/approval/providers/approvalsProvider.dart';
import 'package:seawork/screens/employee/approval/models/approvalsModel.dart';

class Approval extends ConsumerStatefulWidget {
  const Approval({super.key});

  @override
  ConsumerState<Approval> createState() => _ApprovalState();
}

class _ApprovalState extends ConsumerState<Approval>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool showSearchField = false;
  bool isSearching = false;
  String? activeSearchQuery;
  final TextEditingController searchController = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Set up a listener to update the selected tab provider
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        // Reset offset when changing tabs
        ref.read(tasksOffsetProvider.notifier).state = 0;
        ref.read(selectedTabIndexProvider.notifier).state =
            _tabController.index;

        // Refresh data for the selected tab
        _refreshCurrentTabData();
      }
    });
  }

  void _handleSearch() async {
    if (isSearching) return; // Prevent multiple simultaneous searches

    final searchQuery = searchController.text.trim();
    
    setState(() {
      isSearching = true; // Start loading state
    });

    try {
    if (searchQuery.isEmpty) {
      activeSearchQuery = null;
      await _refreshCurrentTabData();
    } else {
      activeSearchQuery = searchQuery;
      await Future.wait([
      ref
          .read(pendingTasksNotifierProvider.notifier)
          .fetchData(searchQuery: searchQuery),
      ref
          .read(approvedTasksNotifierProvider.notifier)
          .fetchData(searchQuery: searchQuery),
      ref
          .read(rejectedTasksNotifierProvider.notifier)
          .fetchData(searchQuery: searchQuery),
      ref
          .read(withdrawnTasksNotifierProvider.notifier)
          .fetchData(searchQuery: searchQuery),
    ]);
  }
    } catch (e) {
      // Handle search error if needed
      print('Search error: $e');
    } finally {
      setState(() {
        isSearching = false; // End loading state
      });
    }
  }

  Future<void> _refreshCurrentTabData() async {
    final searchQuery = activeSearchQuery;
    switch (_tabController.index) {
      case 0:
        await ref
            .read(pendingTasksNotifierProvider.notifier)
            .fetchData(searchQuery: searchQuery);
        break;
      case 1:
        await ref
            .read(approvedTasksNotifierProvider.notifier)
            .fetchData(searchQuery: searchQuery);
        break;
      case 2:
        await ref
            .read(rejectedTasksNotifierProvider.notifier)
            .fetchData(searchQuery: searchQuery);
        break;
      case 3:
        await ref
            .read(withdrawnTasksNotifierProvider.notifier)
            .fetchData(searchQuery: searchQuery);
        break;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    searchController.dispose();
    searchFocusNode.dispose();
    super.dispose();
  }

  /// Intercepts Android back key / iOS back swipe.
  void _handleSystemBack() {
    // Navigate back to dashboard when user hits system-back
    context.go('/dashboard');
  }

  @override
  Widget build(BuildContext context) {
    final taskCountsAsync = ref.watch(taskCountsFutureProvider);
    final isLoading = taskCountsAsync.isLoading;

    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        _handleSystemBack();
      },
      child: Scaffold(
        extendBody: true,
        backgroundColor: AppColors.secondaryColor,
        appBar: CustomAppBar(title: 'Approvals'),
      body:
          isLoading
              ? const Center(child: RotatingLoaderWithMessages())
              : Padding(
                padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top,
                  bottom: MediaQuery.of(context).padding.bottom,
                ),
                child: Column(
                  children: [
                    const SizedBox(height: 8),
                    // Status tab bar from task counts
                    taskCountsAsync.when(
                      loading: () => const SizedBox(height: 40),
                      error:
                          (error, stack) => Center(
                            child: Text('Error loading task counts: $error'),
                          ),
                      data: (taskCounts) {
                        final tabs = [
                          StatusTab(
                            label: 'Pending',
                            count: taskCounts.ASSIGNED?.totalResults ?? 0,
                          ),
                          StatusTab(
                            label: 'Approved',
                            count: taskCounts.COMPLETED?.totalResults ?? 0,
                          ),
                          StatusTab(
                            label: 'Rejected',
                            count: taskCounts.SUSPENDED?.totalResults ?? 0,
                          ),
                          StatusTab(
                            label: 'Withdrawn',
                            count: taskCounts.WITHDRAWN?.totalResults ?? 0,
                          ),
                        ];

                        return Padding(
                          padding: const EdgeInsets.only(left: 24.0),
                          child: CustomStatusTabBar(
                            controller: _tabController,
                            tabs: tabs,
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    // Task content based on selected tab
                    Padding(
                      padding: const EdgeInsets.only(left: 20, right: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          if (showSearchField)
                            Expanded(
                              child: CustomSearchField(
                                showSearchField: showSearchField,
                                searchController: searchController,
                                focusNode: searchFocusNode,
                                toggleSearch: () {
                                  if (!isSearching) {
                                    setState(() {
                                      showSearchField = !showSearchField;
                                      if (!showSearchField) {
                                        searchController.clear();
                                        activeSearchQuery = null;
                                        _refreshCurrentTabData();
                                      }
                                    });
                                  }
                                },
                                onSearch: () => _handleSearch(),
                                onClear: () {
                                  activeSearchQuery = null;
                                  _refreshCurrentTabData();
                                },
                                isLoading: isSearching,
                              ),
                            )
                          else
                            IconButton(
                              icon: isSearching
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                          AppColors.primaryColor,
                                        ),
                                      ),
                                    )
                                  : CustomSvgImage(imageName: "search_icon"),
                              onPressed: isSearching
                                  ? null
                                  : () {
                                      setState(() {
                                        showSearchField = !showSearchField;
                                      });
                                    },
                            ),
                          IgnorePointer(
                            ignoring: isSearching,
                            child: Opacity(
                              opacity: isSearching ? 0.5 : 1.0,
                              child: FilterBasedOnDates(
                                isApproval: true,
                                dateRangeProvider: approvalDateRangeProvider,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 20, right: 20),
                      child: Consumer(
                        builder: (context, ref, child) {
                          // final dateRange = ref.watch(dateRangeProvider);
                          final dateRange = ref.watch(
                            approvalDateRangeProvider,
                          );

                          if (dateRange == null ||
                              dateRange['startDate'] == null ||
                              dateRange['endDate'] == null) {
                            return const SizedBox.shrink();
                          }

                          final startDate = DateFormat(
                            'dd MMM yyyy',
                          ).format(dateRange['startDate']!);
                          final endDate = DateFormat(
                            'dd MMM yyyy',
                          ).format(dateRange['endDate']!);

                          return Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Date range on the left
                                OpenSansText(
                                  '$startDate - $endDate',
                                  fontSize: 14,
                                  color: AppColors.viewColor,
                                  fontWeight: FontWeight.w400,
                                ),

                                // Clear filters button on the right
                                TextButton(
                                  onPressed: isSearching
                                      ? null // Disable when searching
                                      : () {
                                    ref.watch(taskCountsFutureProvider);
                                    ref
                                        .read(
                                          approvalDateRangeProvider.notifier,
                                        )
                                        .state = null;
                                  },
                                  child: OpenSansText(
                                    'clear filters',
                                    fontSize: 12,
                                    color: isSearching 
                                        ? AppColors.viewColor.withOpacity(0.5)
                                        : AppColors.viewColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          // Pending tasks (ASSIGNED)
                          _buildTasksView(
                            status: 'ASSIGNED',
                            tasksAsync: ref.watch(pendingTasksNotifierProvider),
                            index: 0,
                          ),

                          // Approved tasks (COMPLETED)
                          _buildTasksView(
                            status: 'COMPLETED',
                            tasksAsync: ref.watch(
                              approvedTasksNotifierProvider,
                            ),
                            index: 1,
                          ),

                          // Rejected tasks (SUSPENDED)
                          _buildTasksView(
                            status: 'SUSPENDED',
                            tasksAsync: ref.watch(
                              rejectedTasksNotifierProvider,
                            ),
                            index: 2,
                          ),

                          // Withdrawn tasks (WITHDRAWN)
                          _buildTasksView(
                            status: 'WITHDRAWN',
                            tasksAsync: ref.watch(
                              withdrawnTasksNotifierProvider,
                            ),
                            index: 3,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      bottomNavigationBar: CustomBottomNavigationBar(onTap: (p0) {}),
      ),
    );
  }

  // Helper method to build task views
  Widget _buildTasksView({
    required String status,
    required AsyncValue<List<TaskItem>> tasksAsync,
    required int index,
  }) {
    return tasksAsync.when(
      loading: () => const Center(child: CustomLoadingWidget()),
      error: (error, stack) => Center(child: Text('Error: $error')),
      // data: (tasks) => ReusableCard(
      data: (tasks) {
        if (tasks.isEmpty) {
          return noDataFoundWidget(context);
        }
        return ReusableCard(
          status: _mapStatusToLabel(status),
          showDownloadButton: false,
          tasks: _convertToTaskListResponse(
            tasks,
            ref.read(_getNotifierForStatus(status).notifier).hasMore,
          ),
          selectedTabIndex: index,
          iconClicked: 'Approval',
          selectedStatus: status,
          isVisible: true,
          hideContainerForScreens: false,
        );
      },
    );
  }

  // Helper to convert a list of TaskItems to TaskListResponse
  TaskListResponse _convertToTaskListResponse(
    List<TaskItem> tasks,
    bool hasMore,
  ) {
    return TaskListResponse(
      count: tasks.length,
      hasMore: hasMore,
      items: tasks,
    );
  }

  // Helper to map status code to display label
  String _mapStatusToLabel(String status) {
    switch (status) {
      case 'ASSIGNED':
        return 'pending';
      case 'COMPLETED':
        return 'approved';
      case 'SUSPENDED':
        return 'rejected';
      case 'WITHDRAWN':
        return 'withdrawn';
      default:
        return status.toLowerCase();
    }
  }

  // Helper to get the appropriate notifier for a status
  StateNotifierProvider<TaskListNotifier, AsyncValue<List<TaskItem>>>
  _getNotifierForStatus(String status) {
    switch (status) {
      case 'ASSIGNED':
        return pendingTasksNotifierProvider;
      case 'COMPLETED':
        return approvedTasksNotifierProvider;
      case 'SUSPENDED':
        return rejectedTasksNotifierProvider;
      case 'WITHDRAWN':
        return withdrawnTasksNotifierProvider;
      default:
        throw Exception('Unknown status: $status');
    }
  }
}
