import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:seawork/data/preferencesUtils.dart';
import '../repository/tokenhelp.dart';

class EventRepository {
  final String _baseUrl = 'https://graph.microsoft.com/v1.0';
  final String _preferencesTokenKey = PreferencesUtils.SESSION_TOKEN;

  Future<String> _getValidatedAccessToken() async {
    print('[EventRepository] Retrieving and validating token');
    try {
      // Use the token helper instead of extracting from JWT
      final microsoftToken =
          await MicrosoftTokenHelper.getFreshMicrosoftToken();
      print('[EventRepository] Valid Microsoft access token found');
      return microsoftToken;
    } catch (e) {
      print('[EventRepository] Token refresh failed: $e');
      throw Exception('Authentication required - please login');
    }
  }

  // Separate methods for different HTTP verbs
  Future<http.Response> _makeGetRequest(
    String endpoint, {
    Map<String, String>? additionalHeaders,
    Map<String, dynamic>? queryParams,
  }) async {
    try {
      final accessToken = await _getValidatedAccessToken();

      final headers = {
        'Authorization': 'Bearer $accessToken', // Use Microsoft token directly
        'Content-Type': 'application/json',
        ...?additionalHeaders,
      };

      print('[EventRepository] GET: $_baseUrl$endpoint');

      final uri = Uri.parse('$_baseUrl$endpoint').replace(
        queryParameters: queryParams?.map((k, v) => MapEntry(k, v.toString())),
      );

      final response = await http.get(uri, headers: headers);
      print('[EventRepository] Response status: ${response.statusCode}');

      if (response.statusCode == 401) {
        print('[EventRepository] Microsoft token validation failed');
        throw Exception('Session expired - token needs renewal');
      }

      return response;
    } catch (e) {
      print('[EventRepository] GET request error: $e');
      rethrow;
    }
  }

  Future<http.Response> _makePostRequest(
    String endpoint, {
    Map<String, String>? additionalHeaders,
    Map<String, dynamic>? body,
  }) async {
    try {
      final accessToken = await _getValidatedAccessToken();

      final headers = {
        'Authorization': 'Bearer $accessToken',
        'Content-Type': 'application/json',
        ...?additionalHeaders,
      };

      print('[EventRepository] POST: $_baseUrl$endpoint');

      final uri = Uri.parse('$_baseUrl$endpoint');

      final response = await http.post(
        uri,
        headers: headers,
        body: body != null ? json.encode(body) : null,
      );

      print('[EventRepository] Response status: ${response.statusCode}');

      if (response.statusCode == 401) {
        print('[EventRepository] Microsoft token validation failed');
        throw Exception('Session expired - token needs renewal');
      }

      return response;
    } catch (e) {
      print('[EventRepository] POST request error: $e');
      rethrow;
    }
  }

  Future<http.Response> _makePatchRequest(
    String endpoint, {
    Map<String, String>? additionalHeaders,
    Map<String, dynamic>? body,
  }) async {
    try {
      final accessToken = await _getValidatedAccessToken();

      final headers = {
        'Authorization': 'Bearer $accessToken',
        'Content-Type': 'application/json',
        ...?additionalHeaders,
      };

      print('[EventRepository] PATCH: $_baseUrl$endpoint');

      final uri = Uri.parse('$_baseUrl$endpoint');

      final response = await http.patch(
        uri,
        headers: headers,
        body: body != null ? json.encode(body) : null,
      );

      print('[EventRepository] Response status: ${response.statusCode}');

      if (response.statusCode == 401) {
        print('[EventRepository] Microsoft token validation failed');
        throw Exception('Session expired - token needs renewal');
      }

      return response;
    } catch (e) {
      print('[EventRepository] PATCH request error: $e');
      rethrow;
    }
  }

  // Rest of the class remains exactly the same
  Future<List<dynamic>> fetchEvents() async {
    try {
      print('[EventRepository] Fetching all events');
      final response = await _makeGetRequest('/me/events');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> events = data['value'];
        print('[EventRepository] Successfully fetched ${events.length} events');
        return events;
      } else {
        throw Exception(
          'Failed to load events. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('[EventRepository] Error in fetchEvents: $e');
      throw Exception('Failed to load events: $e');
    }
  }

  Future<List<dynamic>> fetchEventsForDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      print(
        '[EventRepository] Fetching events for date range: '
        '${startDate.toIso8601String()} to ${endDate.toIso8601String()}',
      );
      // Convert to UTC for consistent API handling
      final startUtc = startDate.toUtc();
      final endUtc = endDate.toUtc();

      final response = await _makeGetRequest(
        '/me/calendarView',
        additionalHeaders: {'Prefer': 'outlook.timezone="UTC"'},
        queryParams: {
          'startDateTime': startUtc.toIso8601String(),
          'endDateTime': endUtc.toIso8601String(),
          '\$top': '999', // Set high limit to get all events
          '\$orderby': 'start/dateTime', // Order by start time
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        final List<dynamic> events = data['value'];
        print(
          '[EventRepository] Successfully fetched ${events.length} events for date range',
        );
        final today = DateTime.now();
        final todayStr = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
        
        for (var event in events) {
          final startTime = event['start']?['dateTime'] ?? '';
          if (startTime.startsWith(todayStr)) {
          }
        }
        
        return events;
      } else {
        throw Exception(
          'Failed to load events. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('[EventRepository] Error in fetchEventsForDateRange: $e');
      throw Exception('Failed to load events: $e');
    }
  }

  Future<Map<String, dynamic>> fetchEventById(String eventId) async {
    try {
      print('[EventRepository] Fetching event with ID: $eventId');
      final response = await _makeGetRequest('/me/events/$eventId');

      if (response.statusCode == 200) {
        final event = json.decode(response.body);
        print(
          '[EventRepository] Successfully fetched event: ${event['subject']}',
        );
        return event;
      } else {
        throw Exception('Failed to load event. Status: ${response.statusCode}');
      }
    } catch (e) {
      print('[EventRepository] Error in fetchEventById: $e');
      throw Exception('Failed to load event: $e');
    }
  }

  // Accept a meeting invitation using Microsoft Graph API
  Future<bool> acceptMeeting(
    String eventId, {
    String? comment,
    bool sendResponse = true,
  }) async {
    try {
      print('[EventRepository] Accepting meeting with ID: $eventId');

      final body = <String, dynamic>{
        'comment': comment ?? '',
        'sendResponse': sendResponse,
      };

      final response = await _makePostRequest(
        '/me/events/$eventId/accept',
        body: body,
      );

      if (response.statusCode == 202) {
        print('[EventRepository] Successfully accepted meeting');
        return true;
      } else {
        print(
          '[EventRepository] Accept failed with status: ${response.statusCode}',
        );
        print('[EventRepository] Response body: ${response.body}');
        throw Exception(
          'Failed to accept meeting. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('[EventRepository] Error accepting meeting: $e');
      throw Exception('Failed to accept meeting: $e');
    }
  }

  // Decline a meeting invitation using Microsoft Graph API
  Future<bool> declineMeeting(
    String eventId, {
    String? comment,
    bool sendResponse = true,
  }) async {
    try {
      print('[EventRepository] Declining meeting with ID: $eventId');

      final body = <String, dynamic>{
        'comment': comment ?? '',
        'sendResponse': sendResponse,
      };

      final response = await _makePostRequest(
        '/me/events/$eventId/decline',
        body: body,
      );

      if (response.statusCode == 202) {
        print('[EventRepository] Successfully declined meeting');
        return true;
      } else {
        print(
          '[EventRepository] Decline failed with status: ${response.statusCode}',
        );
        print('[EventRepository] Response body: ${response.body}');
        throw Exception(
          'Failed to decline meeting. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('[EventRepository] Error declining meeting: $e');
      throw Exception('Failed to decline meeting: $e');
    }
  }

  // Generic RSVP method that calls specific accept/decline methods
  Future<bool> rsvpToMeeting(
    String eventId,
    String response, {
    String? comment,
    bool sendResponse = true,
  }) async {
    switch (response.toLowerCase()) {
      case 'accept':
      case 'accepted':
        return acceptMeeting(
          eventId,
          comment: comment,
          sendResponse: sendResponse,
        );
      case 'decline':
      case 'declined':
        return declineMeeting(
          eventId,
          comment: comment,
          sendResponse: sendResponse,
        );
      default:
        throw Exception(
          'Invalid RSVP response: $response. Use "accept" or "decline".',
        );
    }
  }

  // Update meeting response using PATCH method
  Future<bool> updateMeetingResponse(
    String eventId,
    String responseStatus,
  ) async {
    try {
      print(
        '[EventRepository] Updating meeting response for event $eventId to: $responseStatus',
      );

      final body = {
        'responseStatus': {
          'response':
              responseStatus, // 'accepted', 'declined', 'tentativelyAccepted'
          'time': DateTime.now().toIso8601String(),
        },
      };

      final response = await _makePatchRequest(
        '/me/events/$eventId',
        body: body,
      );

      if (response.statusCode == 200) {
        print('[EventRepository] Successfully updated meeting response');
        return true;
      } else {
        print(
          '[EventRepository] Update failed with status: ${response.statusCode}',
        );
        throw Exception(
          'Failed to update response. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('[EventRepository] Error in updateMeetingResponse: $e');
      throw Exception('Failed to update meeting response: $e');
    }
  }
}
