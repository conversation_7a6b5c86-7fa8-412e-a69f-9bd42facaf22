import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seawork/components/commonWidgets/customText.dart';
import 'package:seawork/components/widget/customAppbar.dart';
import 'package:seawork/components/widget/customAttachment.dart';
import 'package:seawork/components/widget/headingText.dart';
import 'package:seawork/screens/account/Document/components/documentForm.dart';
import 'package:seawork/screens/account/Document/components/documentTypeselector.dart';
import 'package:seawork/screens/account/Document/components/submitRecordButton.dart';
import 'package:seawork/screens/account/Document/model/documentTypeModel.dart';
import 'package:seawork/screens/account/Document/provider/documentPostPovider.dart';
import 'package:seawork/screens/account/Document/provider/documentTypePovider.dart';
import 'package:seawork/screens/employee/letterRequest/noc.dart';
import 'package:seawork/utils/path/dynamicLinkFieldsProvider/dynamicLinkFields.dart';
import 'package:seawork/utils/style/colors.dart';
import 'dart:convert';

class RequestDetailNotifier extends StateNotifier<RequestDetailState> {
  RequestDetailNotifier() : super(RequestDetailState());
}

class RequestDetailState {}

final requestDetailProvider =
    StateNotifierProvider<RequestDetailNotifier, RequestDetailState>(
      (ref) => RequestDetailNotifier(),
    );

class UploadDocument extends ConsumerStatefulWidget {
  final MyDocumentTypeModel? selectedDocumentType;

  const UploadDocument({Key? key, this.selectedDocumentType}) : super(key: key);

  @override
  _UploadPassportScreenState createState() => _UploadPassportScreenState();
}

class _UploadPassportScreenState extends ConsumerState<UploadDocument> {
  // Controllers
  final TextEditingController personNameController = TextEditingController();
  final TextEditingController passportNumberController =
      TextEditingController();
  final TextEditingController visaIssueDateController = TextEditingController();
  final TextEditingController visaLocationController = TextEditingController();
  final TextEditingController issuingAuthorityController =
      TextEditingController();
  final TextEditingController visaExpiryDateController =
      TextEditingController();
  final TextEditingController accountNumberController = TextEditingController();
  final TextEditingController ibanNumberController = TextEditingController();
  final TextEditingController bankNumberController = TextEditingController();
  final TextEditingController branchNameController = TextEditingController();
  final TextEditingController bisCodeController = TextEditingController();
  final TextEditingController vaccineDateController = TextEditingController();
  final TextEditingController dateOfBirthController = TextEditingController();
  final TextEditingController placeOfBirthController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController emiratesIdDateController =
      TextEditingController();
  final TextEditingController emiratesIdNumberController =
      TextEditingController();
  final TextEditingController residencyVisaUidController =
      TextEditingController();
  final TextEditingController fileNumberController = TextEditingController();
  final TextEditingController nameOfMotherController = TextEditingController();
  final TextEditingController nameOfFatherController = TextEditingController();
  final TextEditingController issuingCommentsController =
      TextEditingController();
  final TextEditingController certificateNameController =
      TextEditingController();
  final TextEditingController certificateNumberController =
      TextEditingController();
  final TextEditingController certificateFromDateController =
      TextEditingController();
  final TextEditingController certificateToDateController =
      TextEditingController();
  final TextEditingController certificateIssuedOnController =
      TextEditingController();
  final TextEditingController certificateIssuingLocationController =
      TextEditingController();
  final TextEditingController certificateIssuingAuthorityController =
      TextEditingController();
  final TextEditingController certificateIssuingCommentsController =
      TextEditingController();
  final TextEditingController visaNumberController = TextEditingController();

  // State variables
  String? selectedDocumentType;
  String? selectedVaccineStatus;
  String? selectedNationality;
  String? selectedMotherNationality;
  String? selectedFatherNationality;
  String? selectedOption = 'Yes';
  String? selectedIssuingCountry;
  bool _isSubmitting = false;
  List<FileModel> uploadedFiles = [];
  bool _shouldPopOnTap = false;

  final Map<String, String> _categoryNameMap = {
    'Attached university certificate': 'UnivCert',
    'Certificate Equivalency': 'CertEquiv',
    'Experience Letter': 'ExpLetter',
    'Police Clearance Certificate': 'PoliceCert',
    'Spouse\'s Detailed Salary Certificate': 'SpouseSalaryCert',
    'University Certificate': 'UnivCert',
    'Work Medical Fitness': 'MedFitness',
    'Passport': 'Passport',
    'Residency Visa': 'ResVisa',
    'New Bank Account Details': 'BankAcct',
    'Birth Certificate': 'BirthCert',
    'Vaccine Status': 'Vaccine',
    'Emirates ID': 'EmiratesID',
    'COVID PCR Report': 'PCRReport',
    'UAE Family Book': 'UAEFamily',
    'Marriage Certificate': 'MarriageCert',
  };

  @override
  void initState() {
    super.initState();
    if (widget.selectedDocumentType != null) {
      selectedDocumentType = widget.selectedDocumentType!.documentType;
    }
    _setupControllerListeners();
  }

  void _setupControllerListeners() {
    final controllers = [
      visaNumberController,
      personNameController,
      passportNumberController,
      visaIssueDateController,
      visaLocationController,
      issuingAuthorityController,
      visaExpiryDateController,
      accountNumberController,
      ibanNumberController,
      bankNumberController,
      branchNameController,
      bisCodeController,
      vaccineDateController,
      dateOfBirthController,
      placeOfBirthController,
      emiratesIdNumberController,
      residencyVisaUidController,
      fileNumberController,
      nameOfMotherController,
      nameOfFatherController,
      issuingCommentsController,
      certificateNameController,
      certificateNumberController,
      certificateFromDateController,
      certificateToDateController,
      certificateIssuedOnController,
      certificateIssuingLocationController,
      certificateIssuingAuthorityController,
      certificateIssuingCommentsController,
      _dateController,
      emiratesIdDateController,
    ];

    for (var controller in controllers) {
      controller.addListener(_updateState);
    }
  }

  @override
  void dispose() {
    _clearAttachments();
    _disposeControllers();
    super.dispose();
  }

  void _disposeControllers() {
    final controllers = [
      visaNumberController,
      personNameController,
      passportNumberController,
      visaIssueDateController,
      visaLocationController,
      issuingAuthorityController,
      visaExpiryDateController,
      accountNumberController,
      ibanNumberController,
      bankNumberController,
      branchNameController,
      bisCodeController,
      vaccineDateController,
      dateOfBirthController,
      placeOfBirthController,
      emiratesIdNumberController,
      residencyVisaUidController,
      fileNumberController,
      nameOfMotherController,
      nameOfFatherController,
      issuingCommentsController,
      certificateNameController,
      certificateNumberController,
      certificateFromDateController,
      certificateToDateController,
      certificateIssuedOnController,
      certificateIssuingLocationController,
      certificateIssuingAuthorityController,
      certificateIssuingCommentsController,
      _dateController,
      emiratesIdDateController,
    ];

    for (var controller in controllers) {
      controller.dispose();
    }
  }

  void _updateState() => setState(() {});

  void _onOptionChanged(String? value) {
    setState(() {
      selectedOption = value;
      if (selectedDocumentType == 'Upload Vaccine Status') {
        selectedVaccineStatus = value;
      }
    });
  }

  void _clearAttachments() => setState(() => uploadedFiles.clear());

  void _clearAllFields() {
    // Clear all text controllers
    for (var controller in [
      visaNumberController,
      personNameController,
      passportNumberController,
      visaIssueDateController,
      visaLocationController,
      issuingAuthorityController,
      visaExpiryDateController,
      accountNumberController,
      ibanNumberController,
      bankNumberController,
      branchNameController,
      bisCodeController,
      vaccineDateController,
      dateOfBirthController,
      placeOfBirthController,
      emiratesIdNumberController,
      residencyVisaUidController,
      fileNumberController,
      nameOfMotherController,
      nameOfFatherController,
      issuingCommentsController,
      certificateNameController,
      certificateNumberController,
      certificateFromDateController,
      certificateToDateController,
      certificateIssuedOnController,
      certificateIssuingLocationController,
      certificateIssuingAuthorityController,
      certificateIssuingCommentsController,
      _dateController,
      emiratesIdDateController,
    ]) {
      controller.clear();
    }

    // Clear all dropdown selections
    setState(() {
      selectedVaccineStatus = null;
      selectedNationality = null;
      selectedMotherNationality = null;
      selectedFatherNationality = null;
      selectedOption = 'Yes';
      selectedIssuingCountry = null;
    });

    // Clear attachments
    _clearAttachments();
  }

  bool _areRequiredFieldsFilled() {
    if (selectedDocumentType == null || uploadedFiles.isEmpty) return false;
    final minimalRequiredDocTypes = [
      'Upload Attested University Certificates',
      'Upload Certificate Equivalency',
      'Upload Experience Letter',
      'Upload Police Clearance Certificate',
      'Upload Spouse\'s Detailed Salary Certificate',
      'Upload University Certificate',
      'Upload Work Medical Fitness',
      'Upload Marriage Certificate',
      'Upload UAE Family Book',
    ];
    if (minimalRequiredDocTypes.contains(selectedDocumentType)) {
      return true;
    }
    switch (selectedDocumentType) {
      case 'Upload Passport':
        return personNameController.text.isNotEmpty &&
            passportNumberController.text.isNotEmpty &&
            visaIssueDateController.text.isNotEmpty &&
            visaExpiryDateController.text.isNotEmpty;
      case 'Upload Residency Visa':
        return personNameController.text.isNotEmpty &&
            visaExpiryDateController.text.isNotEmpty &&
            residencyVisaUidController.text.isNotEmpty &&
            fileNumberController.text.isNotEmpty;
      case 'Upload New Bank Account Details':
        return accountNumberController.text.isNotEmpty &&
            ibanNumberController.text.isNotEmpty &&
            bankNumberController.text.isNotEmpty &&
            branchNameController.text.isNotEmpty;
      case 'Upload Birth Certificate':
        return personNameController.text.isNotEmpty &&
            dateOfBirthController.text.isNotEmpty &&
            placeOfBirthController.text.isNotEmpty &&
            ref.read(countryProvider) != null &&
            nameOfMotherController.text.isNotEmpty &&
            ref.read(countryProvider) != null &&
            nameOfFatherController.text.isNotEmpty &&
            ref.read(countryProvider) != null;
      case 'Upload Vaccine Status':
        return vaccineDateController.text.isNotEmpty &&
            selectedVaccineStatus != null;
      case 'Upload Emirates ID':
        return personNameController.text.isNotEmpty &&
            emiratesIdNumberController.text.isNotEmpty;

      case 'Upload Marriage Certificate':
        return uploadedFiles.isNotEmpty;
      case 'Upload COVID PCR Report':
        return _dateController.text.isNotEmpty && selectedOption != null;
      // case 'Upload UAE Family Book':
      //   return personNameController.text.isNotEmpty &&
      //       visaLocationController.text.isNotEmpty &&
      //       visaNumberController.text.isNotEmpty;
      default:
        return false;
    }
  }

  Future<void> _submitDocument() async {
    if (!_areRequiredFieldsFilled() ||
        _isSubmitting ||
        selectedDocumentType == null) {
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      final documentTypes = await ref.read(myDocumentTypesProvider.future);
      final selectedDocType =
          widget.selectedDocumentType ??
          documentTypes.firstWhere(
            (doc) => doc.documentType == selectedDocumentType,
            orElse:
                () =>
                    MyDocumentTypeModel(documentType: '', documentTypeId: null),
          );

      if (selectedDocType.documentType!.isEmpty ||
          selectedDocType.documentTypeId == null) {
        throw Exception(
          'Selected document type not found or missing ID in repository',
        );
      }

      String documentTypeName = selectedDocumentType!.replaceAll('Upload ', '');
      String categoryName =
          _categoryNameMap[documentTypeName] ?? documentTypeName;

      List<Map<String, String>> attachments =
          uploadedFiles.map((file) {
            return {
              "DatatypeCode": "FILE",
              "FileContents": base64Encode(file.bytes),
              "Description": selectedDocumentType!,
              "CategoryName": categoryName,
              "FileName": file.name,
            };
          }).toList();
      uploadedFiles.map((file) {
        return {
          "DatatypeCode": "FILE",
          "FileContents": base64Encode(file.bytes),
          "Description": selectedDocumentType!,
          "CategoryName": categoryName,
          "FileName": file.name,
        };
      }).toList();

      final Map<String, dynamic> payload = {
        "PersonId": 300000015297106,
        "PersonNumber": "SEN00127",
        "DocumentType": selectedDocType.documentType,
        "DocumentTypeId": selectedDocType.documentTypeId?.toString() ?? '',
        "DocumentCode":
            "${selectedDocumentType?.replaceAll(' ', '_').toUpperCase()}_${DateTime.now().millisecondsSinceEpoch}",
        "attachments": attachments,
      };

      print('Final Payload: $payload');

      final repository = ref.read(documentPostRepositoryProvider);
      final response = await repository.submitDocument(payload);

      if (response != null) {
        _showSuccessMessage();
        _clearAllFields();
        setState(() => _shouldPopOnTap = true);
      } else {
        _showErrorMessage(
          'Failed to submit document. Please check all fields.',
        );
      }
    } catch (e) {
      _showErrorMessage('Submission error: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  void _showSuccessMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: OpenSansText(
          'Document submitted successfully',
          fontSize: 12,
          color: AppColors.blackColor,
        ),
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: OpenSansText(message, fontSize: 12, color: AppColors.red),
      ),
    );
  }

  void _handleScreenTap() {
    if (_shouldPopOnTap && !_isSubmitting) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final heightFromTop = 122.0;
    final maxHeight = mediaQuery.size.height - heightFromTop;

    return WillPopScope(
      onWillPop: () async {
        _clearAttachments();
        return true;
      },
      child: Scaffold(
        backgroundColor: AppColors.secondaryColor,
        appBar: CustomAppBar(title: 'Upload Document', showActionIcon: true),
        body: Form(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(20.0, 16.0, 20.0, 10.0),
            child: Column(
              children: [
                // Content
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        DocumentTypeSelector(
                          selectedDocumentType: selectedDocumentType,
                          onDocumentTypeSelected: (String? newType) {
                            setState(() {
                              selectedDocumentType = newType;
                            });
                          },
                        ),

                        DocumentFormFields(
                          selectedDocumentType: selectedDocumentType,
                          selectedOption: selectedOption,
                          selectedVaccineStatus: selectedVaccineStatus,
                          personNameController: personNameController,
                          passportNumberController: passportNumberController,
                          visaIssueDateController: visaIssueDateController,
                          visaLocationController: visaLocationController,
                          issuingAuthorityController:
                              issuingAuthorityController,
                          visaExpiryDateController: visaExpiryDateController,
                          accountNumberController: accountNumberController,
                          ibanNumberController: ibanNumberController,
                          bankNumberController: bankNumberController,
                          branchNameController: branchNameController,
                          bisCodeController: bisCodeController,
                          vaccineDateController: vaccineDateController,
                          dateOfBirthController: dateOfBirthController,
                          placeOfBirthController: placeOfBirthController,
                          emiratesIdDateController: emiratesIdDateController,
                          emiratesIdNumberController:
                              emiratesIdNumberController,
                          residencyVisaUidController:
                              residencyVisaUidController,
                          fileNumberController: fileNumberController,
                          nameOfMotherController: nameOfMotherController,
                          nameOfFatherController: nameOfFatherController,
                          issuingCommentsController: issuingCommentsController,
                          certificateNameController: certificateNameController,
                          certificateNumberController:
                              certificateNumberController,
                          certificateFromDateController:
                              certificateFromDateController,
                          certificateToDateController:
                              certificateToDateController,
                          certificateIssuedOnController:
                              certificateIssuedOnController,
                          certificateIssuingLocationController:
                              certificateIssuingLocationController,
                          certificateIssuingAuthorityController:
                              certificateIssuingAuthorityController,
                          certificateIssuingCommentsController:
                              certificateIssuingCommentsController,
                          dateController: _dateController,
                          onOptionChanged: _onOptionChanged,
                        ),
                        if (selectedDocumentType == 'Upload Vaccine Status' ||
                            selectedDocumentType == 'Upload Birth Certificate')
                          const SizedBox(height: 24),
                        HeadingText(text: 'Attachments', hasAsterisk: true),
                        const SizedBox(height: 8),

                        AttachmentField(
                          uploadedFiles: uploadedFiles,
                          onFilesChanged: (files) {
                            setState(() => uploadedFiles = files);
                          },
                        ),

                        if (selectedDocumentType ==
                            'Upload New Bank Account Details') ...[
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              OpenSansText(
                                'Note : Upload stamped letter from bank for the new account',
                                fontSize: 11,
                                color: AppColors.blackColor,
                                fontWeight: FontWeight.w400,
                                textAlign: TextAlign.left,
                              ),
                            ],
                          ),
                        ],
                        const SizedBox(height: 24),
                        if (selectedDocumentType !=
                            'Upload Birth Certificate') ...[
                          DynamicLinkFields(),
                        ],

                        Padding(
                          padding: const EdgeInsets.only(top: 24, bottom: 32),
                          child: SubmitDocumentRecord(
                            isSubmitting: _isSubmitting,
                            areRequiredFieldsFilled: _areRequiredFieldsFilled,
                            submitDocument: _submitDocument,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
